rm -rf /tmp/*test-raftstore*
GO111MODULE=on go test -v --count=1 --parallel=1 -p=1 --timeout=300s ./raft -run 2C || true
=== RUN   TestRestoreSnapshot2C
--- PASS: TestRestoreSnapshot2C (0.00s)
=== RUN   TestRestoreIgnoreSnapshot2C
--- PASS: TestRestoreIgnoreSnapshot2C (0.00s)
=== RUN   TestProvideSnap2C
--- PASS: TestProvideSnap2C (0.00s)
=== RUN   TestRestoreFromSnapMsg2C
--- PASS: TestRestoreFromSnapMsg2C (0.00s)
=== RUN   TestRestoreFromSnapWithOverlapingPeersMsg2C
--- PASS: TestRestoreFromSnapWithOverlapingPeersMsg2C (0.00s)
=== RUN   TestSlowNodeRestore2C
--- PASS: TestSlowNodeRestore2C (0.00s)
=== RUN   TestRawNodeRestartFromSnapshot2C
    rawnode_test.go:245: g = raft.Ready{SoftState:(*raft.SoftState)(nil), HardState:eraftpb.HardState{Term:0x0, Vote:0x0, Commit:0x0, XXX_NoUnkeyedLiteral:struct {}{}, XXX_unrecognized:[]uint8(nil), XXX_sizecache:0}, Entries:[]eraftpb.Entry(nil), Snapshot:eraftpb.Snapshot{Data:[]uint8(nil), Metadata:(*eraftpb.SnapshotMetadata)(nil), XXX_NoUnkeyedLiteral:struct {}{}, XXX_unrecognized:[]uint8(nil), XXX_sizecache:0}, CommittedEntries:[]eraftpb.Entry{eraftpb.Entry{EntryType:0, Term:0x1, Index:0x3, Data:[]uint8{0x66, 0x6f, 0x6f}, XXX_NoUnkeyedLiteral:struct {}{}, XXX_unrecognized:[]uint8(nil), XXX_sizecache:0}}, Messages:[]eraftpb.Message(nil)},
                     w   raft.Ready{SoftState:(*raft.SoftState)(nil), HardState:eraftpb.HardState{Term:0x0, Vote:0x0, Commit:0x0, XXX_NoUnkeyedLiteral:struct {}{}, XXX_unrecognized:[]uint8(nil), XXX_sizecache:0}, Entries:[]eraftpb.Entry{}, Snapshot:eraftpb.Snapshot{Data:[]uint8(nil), Metadata:(*eraftpb.SnapshotMetadata)(nil), XXX_NoUnkeyedLiteral:struct {}{}, XXX_unrecognized:[]uint8(nil), XXX_sizecache:0}, CommittedEntries:[]eraftpb.Entry{eraftpb.Entry{EntryType:0, Term:0x1, Index:0x3, Data:[]uint8{0x66, 0x6f, 0x6f}, XXX_NoUnkeyedLiteral:struct {}{}, XXX_unrecognized:[]uint8(nil), XXX_sizecache:0}}, Messages:[]eraftpb.Message(nil)}
    rawnode_test.go:250: unexpected Ready: true
--- FAIL: TestRawNodeRestartFromSnapshot2C (0.00s)
FAIL
FAIL	github.com/pingcap-incubator/tinykv/raft	0.527s
FAIL
GO111MODULE=on go test -v --count=1 --parallel=1 -p=1 --timeout=300s ./kv/test_raftstore -run ^TestSnapshotUnreliableRecoverConcurrentPartition2C|| true
=== RUN   TestSnapshotUnreliableRecoverConcurrentPartition2C
2025/06/29 20:28:24.069349 node.go:193: [0;37m[info] start raft store node, storeID: 1[0m
2025/06/29 20:28:24.070122 peer.go:41: [0;37m[info] region id:1 region_epoch:<conf_ver:1 version:1 > peers:<id:4 store_id:4 > peers:<id:5 store_id:5 > peers:<id:1 store_id:1 > peers:<id:2 store_id:2 > peers:<id:3 store_id:3 >  create peer with ID 1[0m
2025/06/29 20:28:24.070209 raftstore.go:174: [0;37m[info] start store 1, region_count 1, tombstone_count 0, takes 226.291µs[0m
2025/06/29 20:28:24.070770 node.go:193: [0;37m[info] start raft store node, storeID: 2[0m
2025/06/29 20:28:24.070958 peer.go:41: [0;37m[info] region id:1 region_epoch:<conf_ver:1 version:1 > peers:<id:4 store_id:4 > peers:<id:5 store_id:5 > peers:<id:1 store_id:1 > peers:<id:2 store_id:2 > peers:<id:3 store_id:3 >  create peer with ID 2[0m
2025/06/29 20:28:24.070977 raftstore.go:174: [0;37m[info] start store 2, region_count 1, tombstone_count 0, takes 28.25µs[0m
2025/06/29 20:28:24.071450 node.go:193: [0;37m[info] start raft store node, storeID: 3[0m
2025/06/29 20:28:24.071617 peer.go:41: [0;37m[info] region id:1 region_epoch:<conf_ver:1 version:1 > peers:<id:4 store_id:4 > peers:<id:5 store_id:5 > peers:<id:1 store_id:1 > peers:<id:2 store_id:2 > peers:<id:3 store_id:3 >  create peer with ID 3[0m
2025/06/29 20:28:24.071635 raftstore.go:174: [0;37m[info] start store 3, region_count 1, tombstone_count 0, takes 28.25µs[0m
2025/06/29 20:28:24.072137 node.go:193: [0;37m[info] start raft store node, storeID: 4[0m
2025/06/29 20:28:24.072308 peer.go:41: [0;37m[info] region id:1 region_epoch:<conf_ver:1 version:1 > peers:<id:4 store_id:4 > peers:<id:5 store_id:5 > peers:<id:1 store_id:1 > peers:<id:2 store_id:2 > peers:<id:3 store_id:3 >  create peer with ID 4[0m
2025/06/29 20:28:24.072325 raftstore.go:174: [0;37m[info] start store 4, region_count 1, tombstone_count 0, takes 26.292µs[0m
2025/06/29 20:28:24.072804 node.go:193: [0;37m[info] start raft store node, storeID: 5[0m
2025/06/29 20:28:24.072991 peer.go:41: [0;37m[info] region id:1 region_epoch:<conf_ver:1 version:1 > peers:<id:4 store_id:4 > peers:<id:5 store_id:5 > peers:<id:1 store_id:1 > peers:<id:2 store_id:2 > peers:<id:3 store_id:3 >  create peer with ID 5[0m
2025/06/29 20:28:24.073012 raftstore.go:174: [0;37m[info] start store 5, region_count 1, tombstone_count 0, takes 35.708µs[0m
2025/06/29 20:28:25.373202 test_test.go:104: [0;37m[info] partition: [1 5], [2 3 4][0m
2025/06/29 20:28:25.427236 peer_storage.go:182: [0;37m[info] [region 1] 2 requesting snapshot[0m
2025/06/29 20:28:25.428801 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore706647814/snap/gen_1_6_1807_(default|write|lock).sst, key count 908, size 23966[0m
2025/06/29 20:28:25.430131 peer_storage.go:182: [0;37m[info] [region 1] 2 requesting snapshot[0m
2025/06/29 20:28:25.431247 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore706647814/snap/gen_1_6_1819_(default|write|lock).sst, key count 912, size 24074[0m
2025/06/29 20:28:25.528190 peer_storage.go:182: [0;37m[info] [region 1] 2 requesting snapshot[0m
2025/06/29 20:28:25.529377 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore706647814/snap/gen_1_6_1957_(default|write|lock).sst, key count 979, size 25883[0m
2025/06/29 20:28:25.530573 peer_storage.go:182: [0;37m[info] [region 1] 2 requesting snapshot[0m
2025/06/29 20:28:25.531893 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore706647814/snap/gen_1_6_1971_(default|write|lock).sst, key count 985, size 26045[0m
2025/06/29 20:28:25.626877 peer_storage.go:182: [0;37m[info] [region 1] 2 requesting snapshot[0m
2025/06/29 20:28:25.628103 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore706647814/snap/gen_1_6_2103_(default|write|lock).sst, key count 1051, size 27827[0m
2025/06/29 20:28:25.629175 peer_storage.go:182: [0;37m[info] [region 1] 2 requesting snapshot[0m
2025/06/29 20:28:25.630296 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore706647814/snap/gen_1_6_2119_(default|write|lock).sst, key count 1056, size 27962[0m
2025/06/29 20:28:25.675755 peer_storage.go:182: [0;37m[info] [region 1] 2 requesting snapshot[0m
2025/06/29 20:28:25.677035 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore706647814/snap/gen_1_6_2206_(default|write|lock).sst, key count 1089, size 28853[0m
2025/06/29 20:28:25.681075 peer_storage.go:182: [0;37m[info] [region 1] 2 requesting snapshot[0m
2025/06/29 20:28:25.682698 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore706647814/snap/gen_1_6_2237_(default|write|lock).sst, key count 1109, size 29393[0m
2025/06/29 20:28:25.724717 peer_storage.go:182: [0;37m[info] [region 1] 2 requesting snapshot[0m
2025/06/29 20:28:25.725834 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore706647814/snap/gen_1_6_2313_(default|write|lock).sst, key count 1148, size 30446[0m
2025/06/29 20:28:25.728167 peer_storage.go:182: [0;37m[info] [region 1] 2 requesting snapshot[0m
2025/06/29 20:28:25.729227 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore706647814/snap/gen_1_6_2334_(default|write|lock).sst, key count 1161, size 30797[0m
2025/06/29 20:28:25.775496 peer_storage.go:182: [0;37m[info] [region 1] 2 requesting snapshot[0m
2025/06/29 20:28:25.776809 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore706647814/snap/gen_1_6_2421_(default|write|lock).sst, key count 1205, size 31985[0m
2025/06/29 20:28:25.778360 peer_storage.go:182: [0;37m[info] [region 1] 2 requesting snapshot[0m
2025/06/29 20:28:25.779342 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore706647814/snap/gen_1_6_2439_(default|write|lock).sst, key count 1216, size 32282[0m
2025/06/29 20:28:25.829925 peer_storage.go:182: [0;37m[info] [region 1] 2 requesting snapshot[0m
2025/06/29 20:28:25.831102 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore706647814/snap/gen_1_6_2524_(default|write|lock).sst, key count 1253, size 33281[0m
2025/06/29 20:28:25.832192 peer_storage.go:182: [0;37m[info] [region 1] 2 requesting snapshot[0m
2025/06/29 20:28:25.833248 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore706647814/snap/gen_1_6_2536_(default|write|lock).sst, key count 1257, size 33389[0m
2025/06/29 20:28:25.927098 peer_storage.go:182: [0;37m[info] [region 1] 2 requesting snapshot[0m
2025/06/29 20:28:25.928453 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore706647814/snap/gen_1_6_2671_(default|write|lock).sst, key count 1330, size 35360[0m
2025/06/29 20:28:25.930003 peer_storage.go:182: [0;37m[info] [region 1] 2 requesting snapshot[0m
2025/06/29 20:28:25.931062 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore706647814/snap/gen_1_6_2688_(default|write|lock).sst, key count 1340, size 35630[0m
2025/06/29 20:28:25.977073 peer_storage.go:182: [0;37m[info] [region 1] 2 requesting snapshot[0m
2025/06/29 20:28:25.978315 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore706647814/snap/gen_1_6_2773_(default|write|lock).sst, key count 1384, size 36818[0m
2025/06/29 20:28:25.979331 peer_storage.go:182: [0;37m[info] [region 1] 2 requesting snapshot[0m
2025/06/29 20:28:25.980448 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore706647814/snap/gen_1_6_2786_(default|write|lock).sst, key count 1390, size 36980[0m
2025/06/29 20:28:25.991392 test_test.go:104: [0;37m[info] partition: [5], [1 2 3 4][0m
2025/06/29 20:28:26.025722 peer_storage.go:182: [0;37m[info] [region 1] 2 requesting snapshot[0m
2025/06/29 20:28:26.027157 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore706647814/snap/gen_1_6_2890_(default|write|lock).sst, key count 1441, size 38357[0m
2025/06/29 20:28:26.028198 peer_storage.go:182: [0;37m[info] [region 1] 2 requesting snapshot[0m
2025/06/29 20:28:26.029251 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore706647814/snap/gen_1_6_2906_(default|write|lock).sst, key count 1449, size 38573[0m
2025/06/29 20:28:26.074826 peer_storage.go:182: [0;37m[info] [region 1] 2 requesting snapshot[0m
2025/06/29 20:28:26.076178 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore706647814/snap/gen_1_6_3004_(default|write|lock).sst, key count 1498, size 39896[0m
2025/06/29 20:28:26.077340 peer_storage.go:182: [0;37m[info] [region 1] 2 requesting snapshot[0m
2025/06/29 20:28:26.078420 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore706647814/snap/gen_1_6_3021_(default|write|lock).sst, key count 1504, size 40058[0m
2025/06/29 20:28:26.124930 peer_storage.go:182: [0;37m[info] [region 1] 2 requesting snapshot[0m
2025/06/29 20:28:26.126291 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore706647814/snap/gen_1_6_3119_(default|write|lock).sst, key count 1550, size 41300[0m
2025/06/29 20:28:26.127265 peer_storage.go:182: [0;37m[info] [region 1] 2 requesting snapshot[0m
2025/06/29 20:28:26.128357 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore706647814/snap/gen_1_6_3135_(default|write|lock).sst, key count 1557, size 41489[0m
2025/06/29 20:28:26.533568 test_test.go:104: [0;37m[info] partition: [1 5], [2 3 4][0m
2025/06/29 20:28:27.073633 test_test.go:104: [0;37m[info] partition: [3], [1 2 4 5][0m
2025/06/29 20:28:27.759808 test_test.go:104: [0;37m[info] partition: [3 4 5], [1 2][0m
2025/06/29 20:28:28.409892 test_test.go:104: [0;37m[info] partition: [], [1 2 3 4 5][0m
2025/06/29 20:28:28.927960 test_test.go:104: [0;37m[info] partition: [2 3 4 5], [1][0m
2025/06/29 20:28:29.514996 test_test.go:104: [0;37m[info] partition: [2 4], [1 3 5][0m
2025/06/29 20:28:30.054035 test_test.go:104: [0;37m[info] partition: [1 4 5], [2 3][0m
2025/06/29 20:28:31.022382 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:31.023819 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_3155_(default|write|lock).sst, key count 1569, size 41813[0m
2025/06/29 20:28:31.097099 test_test.go:270: [0;33m[warning] shutdown servers
[0m
2025/06/29 20:28:31.097124 node.go:198: [0;37m[info] stop raft store thread, storeID: 1[0m
2025/06/29 20:28:31.097155 node.go:198: [0;37m[info] stop raft store thread, storeID: 2[0m
2025/06/29 20:28:31.097202 node.go:198: [0;37m[info] stop raft store thread, storeID: 3[0m
2025/06/29 20:28:31.097230 node.go:198: [0;37m[info] stop raft store thread, storeID: 4[0m
2025/06/29 20:28:31.097241 node.go:198: [0;37m[info] stop raft store thread, storeID: 5[0m
2025/06/29 20:28:31.598324 test_test.go:277: [0;33m[warning] restart servers
[0m
2025/06/29 20:28:31.599580 node.go:193: [0;37m[info] start raft store node, storeID: 1[0m
2025/06/29 20:28:31.601033 peer.go:41: [0;37m[info] region id:1 region_epoch:<conf_ver:1 version:1 > peers:<id:4 store_id:4 > peers:<id:5 store_id:5 > peers:<id:1 store_id:1 > peers:<id:2 store_id:2 > peers:<id:3 store_id:3 >  create peer with ID 1[0m
2025/06/29 20:28:31.601217 raftstore.go:174: [0;37m[info] start store 1, region_count 1, tombstone_count 0, takes 356.75µs[0m
2025/06/29 20:28:31.603098 node.go:193: [0;37m[info] start raft store node, storeID: 2[0m
2025/06/29 20:28:31.604338 peer.go:41: [0;37m[info] region id:1 region_epoch:<conf_ver:1 version:1 > peers:<id:4 store_id:4 > peers:<id:5 store_id:5 > peers:<id:1 store_id:1 > peers:<id:2 store_id:2 > peers:<id:3 store_id:3 >  create peer with ID 2[0m
2025/06/29 20:28:31.604448 raftstore.go:174: [0;37m[info] start store 2, region_count 1, tombstone_count 0, takes 172.833µs[0m
2025/06/29 20:28:31.605173 node.go:193: [0;37m[info] start raft store node, storeID: 3[0m
2025/06/29 20:28:31.605474 peer.go:41: [0;37m[info] region id:1 region_epoch:<conf_ver:1 version:1 > peers:<id:4 store_id:4 > peers:<id:5 store_id:5 > peers:<id:1 store_id:1 > peers:<id:2 store_id:2 > peers:<id:3 store_id:3 >  create peer with ID 3[0m
2025/06/29 20:28:31.605551 raftstore.go:174: [0;37m[info] start store 3, region_count 1, tombstone_count 0, takes 105.583µs[0m
2025/06/29 20:28:31.606278 node.go:193: [0;37m[info] start raft store node, storeID: 4[0m
2025/06/29 20:28:31.606472 peer.go:41: [0;37m[info] region id:1 region_epoch:<conf_ver:1 version:1 > peers:<id:4 store_id:4 > peers:<id:5 store_id:5 > peers:<id:1 store_id:1 > peers:<id:2 store_id:2 > peers:<id:3 store_id:3 >  create peer with ID 4[0m
2025/06/29 20:28:31.606533 raftstore.go:174: [0;37m[info] start store 4, region_count 1, tombstone_count 0, takes 82.666µs[0m
2025/06/29 20:28:31.607160 node.go:193: [0;37m[info] start raft store node, storeID: 5[0m
2025/06/29 20:28:31.607323 peer.go:41: [0;37m[info] region id:1 region_epoch:<conf_ver:1 version:1 > peers:<id:4 store_id:4 > peers:<id:5 store_id:5 > peers:<id:1 store_id:1 > peers:<id:2 store_id:2 > peers:<id:3 store_id:3 >  create peer with ID 5[0m
2025/06/29 20:28:31.607377 raftstore.go:174: [0;37m[info] start store 5, region_count 1, tombstone_count 0, takes 74.625µs[0m
2025/06/29 20:28:32.156553 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:32.157928 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_16_3166_(default|write|lock).sst, key count 1571, size 41867[0m
2025/06/29 20:28:32.161181 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:32.162278 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_3177_(default|write|lock).sst, key count 1562, size 41642[0m
2025/06/29 20:28:32.167555 peer_storage.go:336: [0;37m[info] [region 1] 1 begin to apply snapshot[0m
2025/06/29 20:28:32.167670 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 13 raft logs, takes 101.708µs[0m
2025/06/29 20:28:32.167705 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:32.168471 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:32.175746 peer_storage.go:336: [0;37m[info] [region 1] 5 begin to apply snapshot[0m
2025/06/29 20:28:32.175870 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 13 raft logs, takes 108.625µs[0m
2025/06/29 20:28:32.175882 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:32.182677 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:32.212057 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:32.212074 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 43.516708ms][0m
2025/06/29 20:28:32.222213 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:32.222227 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 39.464584ms][0m
2025/06/29 20:28:32.309617 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:32.311860 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_3228_(default|write|lock).sst, key count 1511, size 40317[0m
2025/06/29 20:28:32.314228 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:32.316041 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_3242_(default|write|lock).sst, key count 1498, size 39979[0m
2025/06/29 20:28:32.320418 peer_storage.go:336: [0;37m[info] [region 1] 1 begin to apply snapshot[0m
2025/06/29 20:28:32.320510 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 12 raft logs, takes 76.625µs[0m
2025/06/29 20:28:32.320522 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:32.321875 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:32.330568 peer_storage.go:336: [0;37m[info] [region 1] 5 begin to apply snapshot[0m
2025/06/29 20:28:32.330663 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 12 raft logs, takes 80.083µs[0m
2025/06/29 20:28:32.330673 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:32.334268 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:32.353112 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:32.353131 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 31.18375ms][0m
2025/06/29 20:28:32.363142 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:32.363150 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 28.789084ms][0m
2025/06/29 20:28:32.663948 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:32.666168 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_3329_(default|write|lock).sst, key count 1411, size 37657[0m
2025/06/29 20:28:32.667097 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:32.668965 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_3339_(default|write|lock).sst, key count 1402, size 37414[0m
2025/06/29 20:28:32.673466 peer_storage.go:336: [0;37m[info] [region 1] 5 begin to apply snapshot[0m
2025/06/29 20:28:32.673611 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 11 raft logs, takes 129.167µs[0m
2025/06/29 20:28:32.673627 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:32.674974 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:32.679707 peer_storage.go:336: [0;37m[info] [region 1] 1 begin to apply snapshot[0m
2025/06/29 20:28:32.679825 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 11 raft logs, takes 108.458µs[0m
2025/06/29 20:28:32.679840 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:32.683747 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:32.711883 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:32.711898 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 36.858125ms][0m
2025/06/29 20:28:32.723093 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:32.723122 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 39.288083ms][0m
2025/06/29 20:28:33.064677 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:33.066653 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_3430_(default|write|lock).sst, key count 1311, size 34957[0m
2025/06/29 20:28:33.067694 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:33.069225 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_3439_(default|write|lock).sst, key count 1303, size 34741[0m
2025/06/29 20:28:33.074510 peer_storage.go:336: [0;37m[info] [region 1] 5 begin to apply snapshot[0m
2025/06/29 20:28:33.074713 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 10 raft logs, takes 192.125µs[0m
2025/06/29 20:28:33.074730 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:33.076123 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:33.081493 peer_storage.go:336: [0;37m[info] [region 1] 1 begin to apply snapshot[0m
2025/06/29 20:28:33.081712 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 10 raft logs, takes 206.667µs[0m
2025/06/29 20:28:33.081723 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:33.086410 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:33.113096 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:33.113111 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 36.909ms][0m
2025/06/29 20:28:33.127148 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:33.127163 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 40.677708ms][0m
2025/06/29 20:28:33.512606 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:33.514544 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_3534_(default|write|lock).sst, key count 1209, size 32250[0m
2025/06/29 20:28:33.515465 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:33.517818 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_3543_(default|write|lock).sst, key count 1201, size 32042[0m
2025/06/29 20:28:33.521508 peer_storage.go:336: [0;37m[info] [region 1] 5 begin to apply snapshot[0m
2025/06/29 20:28:33.521889 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 9 raft logs, takes 368.75µs[0m
2025/06/29 20:28:33.521903 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:33.523230 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:33.532439 peer_storage.go:336: [0;37m[info] [region 1] 1 begin to apply snapshot[0m
2025/06/29 20:28:33.532748 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 9 raft logs, takes 296.75µs[0m
2025/06/29 20:28:33.532764 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:33.539679 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:33.558181 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:33.558197 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 34.8965ms][0m
2025/06/29 20:28:33.570112 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:33.570128 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 30.32ms][0m
2025/06/29 20:28:33.913330 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:33.915116 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_3637_(default|write|lock).sst, key count 1107, size 29559[0m
2025/06/29 20:28:33.916089 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:33.917802 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_3646_(default|write|lock).sst, key count 1099, size 29343[0m
2025/06/29 20:28:33.921473 peer_storage.go:336: [0;37m[info] [region 1] 5 begin to apply snapshot[0m
2025/06/29 20:28:33.921904 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 8 raft logs, takes 419.083µs[0m
2025/06/29 20:28:33.921922 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:33.923088 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:33.930585 peer_storage.go:336: [0;37m[info] [region 1] 1 begin to apply snapshot[0m
2025/06/29 20:28:33.930956 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 8 raft logs, takes 356.084µs[0m
2025/06/29 20:28:33.930973 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:33.937460 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:33.956130 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:33.956149 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 32.976916ms][0m
2025/06/29 20:28:33.975124 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:33.975142 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 37.587584ms][0m
2025/06/29 20:28:34.314865 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:34.316207 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_3738_(default|write|lock).sst, key count 1007, size 26859[0m
2025/06/29 20:28:34.317304 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:34.318199 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_3744_(default|write|lock).sst, key count 1002, size 26724[0m
2025/06/29 20:28:34.323698 peer_storage.go:336: [0;37m[info] [region 1] 5 begin to apply snapshot[0m
2025/06/29 20:28:34.324265 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 7 raft logs, takes 556.583µs[0m
2025/06/29 20:28:34.324278 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:34.325454 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:34.333320 peer_storage.go:336: [0;37m[info] [region 1] 1 begin to apply snapshot[0m
2025/06/29 20:28:34.333887 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 7 raft logs, takes 553.666µs[0m
2025/06/29 20:28:34.333902 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:34.337533 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:34.360116 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:34.360133 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 34.600708ms][0m
2025/06/29 20:28:34.379124 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:34.379145 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 41.523792ms][0m
2025/06/29 20:28:34.765205 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:34.767640 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_3839_(default|write|lock).sst, key count 908, size 24221[0m
2025/06/29 20:28:34.769036 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:34.771094 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_3850_(default|write|lock).sst, key count 898, size 23961[0m
2025/06/29 20:28:34.775617 peer_storage.go:336: [0;37m[info] [region 1] 5 begin to apply snapshot[0m
2025/06/29 20:28:34.776385 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 6 raft logs, takes 751.25µs[0m
2025/06/29 20:28:34.776399 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:34.777272 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:34.787353 peer_storage.go:336: [0;37m[info] [region 1] 1 begin to apply snapshot[0m
2025/06/29 20:28:34.788441 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 6 raft logs, takes 1.067583ms[0m
2025/06/29 20:28:34.788458 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:34.789481 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:34.811103 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:34.811124 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 33.772167ms][0m
2025/06/29 20:28:34.823192 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:34.823211 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 33.543834ms][0m
2025/06/29 20:28:35.215919 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:35.217879 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_3941_(default|write|lock).sst, key count 807, size 21569[0m
2025/06/29 20:28:35.218966 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:35.223312 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_3950_(default|write|lock).sst, key count 799, size 21353[0m
2025/06/29 20:28:35.225558 peer_storage.go:336: [0;37m[info] [region 1] 5 begin to apply snapshot[0m
2025/06/29 20:28:35.226497 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 5 raft logs, takes 923.25µs[0m
2025/06/29 20:28:35.226512 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:35.227428 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:35.232105 peer_storage.go:336: [0;37m[info] [region 1] 1 begin to apply snapshot[0m
2025/06/29 20:28:35.232791 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 5 raft logs, takes 676.209µs[0m
2025/06/29 20:28:35.232804 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:35.238330 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:35.265084 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:35.265101 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 37.604208ms][0m
2025/06/29 20:28:35.277089 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:35.277106 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 38.694916ms][0m
2025/06/29 20:28:35.672690 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:35.674044 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_4046_(default|write|lock).sst, key count 703, size 18761[0m
2025/06/29 20:28:35.675083 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:35.675945 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_4053_(default|write|lock).sst, key count 697, size 18599[0m
2025/06/29 20:28:35.680587 peer_storage.go:336: [0;37m[info] [region 1] 5 begin to apply snapshot[0m
2025/06/29 20:28:35.681483 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 4 raft logs, takes 883.333µs[0m
2025/06/29 20:28:35.681496 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:35.682134 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:35.692485 peer_storage.go:336: [0;37m[info] [region 1] 1 begin to apply snapshot[0m
2025/06/29 20:28:35.693279 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 4 raft logs, takes 779.75µs[0m
2025/06/29 20:28:35.693292 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:35.698422 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:35.724090 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:35.724108 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 41.897042ms][0m
2025/06/29 20:28:35.738133 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:35.738168 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 39.643834ms][0m
2025/06/29 20:28:36.061636 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:36.062813 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_4149_(default|write|lock).sst, key count 602, size 16065[0m
2025/06/29 20:28:36.063752 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:36.064644 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_4155_(default|write|lock).sst, key count 597, size 15935[0m
2025/06/29 20:28:36.069626 peer_storage.go:336: [0;37m[info] [region 1] 5 begin to apply snapshot[0m
2025/06/29 20:28:36.070779 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 3 raft logs, takes 1.13925ms[0m
2025/06/29 20:28:36.070795 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:36.071414 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:36.075010 peer_storage.go:336: [0;37m[info] [region 1] 1 begin to apply snapshot[0m
2025/06/29 20:28:36.076263 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 3 raft logs, takes 1.242125ms[0m
2025/06/29 20:28:36.076275 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:36.078361 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:36.108183 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:36.108198 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 36.692083ms][0m
2025/06/29 20:28:36.116083 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:36.116096 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 37.652583ms][0m
2025/06/29 20:28:36.470381 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:36.471809 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_4254_(default|write|lock).sst, key count 498, size 13336[0m
2025/06/29 20:28:36.472679 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:36.474925 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_4261_(default|write|lock).sst, key count 492, size 13174[0m
2025/06/29 20:28:36.479733 peer_storage.go:336: [0;37m[info] [region 1] 5 begin to apply snapshot[0m
2025/06/29 20:28:36.481293 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 2 raft logs, takes 1.547291ms[0m
2025/06/29 20:28:36.481310 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:36.485301 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:36.485736 peer_storage.go:336: [0;37m[info] [region 1] 1 begin to apply snapshot[0m
2025/06/29 20:28:36.486992 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 2 raft logs, takes 1.246458ms[0m
2025/06/29 20:28:36.487006 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:36.489944 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:36.522084 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:36.522101 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 36.701667ms][0m
2025/06/29 20:28:36.535100 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:36.535114 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 45.038667ms][0m
2025/06/29 20:28:37.018971 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:37.021186 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_4356_(default|write|lock).sst, key count 397, size 10609[0m
2025/06/29 20:28:37.022008 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:37.028167 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_4365_(default|write|lock).sst, key count 389, size 10393[0m
2025/06/29 20:28:37.032810 peer_storage.go:336: [0;37m[info] [region 1] 5 begin to apply snapshot[0m
2025/06/29 20:28:37.034277 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 1 raft logs, takes 1.455833ms[0m
2025/06/29 20:28:37.034295 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:37.034719 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:37.037909 peer_storage.go:336: [0;37m[info] [region 1] 1 begin to apply snapshot[0m
2025/06/29 20:28:37.039208 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 1 raft logs, takes 1.288125ms[0m
2025/06/29 20:28:37.039221 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:37.042638 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:37.074229 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:37.074248 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 39.435209ms][0m
2025/06/29 20:28:37.079338 raftlog_gc.go:52: [0;37m[info] no need to gc, [regionId: 1][0m
2025/06/29 20:28:37.080172 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:37.080197 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 37.433166ms][0m
2025/06/29 20:28:38.523154 test_test.go:104: [0;37m[info] partition: [1 4], [2 3 5][0m
2025/06/29 20:28:38.559556 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:38.561910 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_6446_(default|write|lock).sst, key count 839, size 22103[0m
2025/06/29 20:28:38.563091 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:38.565180 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_6469_(default|write|lock).sst, key count 850, size 22400[0m
2025/06/29 20:28:38.611507 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:38.613385 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_6553_(default|write|lock).sst, key count 893, size 23561[0m
2025/06/29 20:28:38.614514 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:38.615520 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_6572_(default|write|lock).sst, key count 905, size 23885[0m
2025/06/29 20:28:38.659570 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:38.660636 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_6656_(default|write|lock).sst, key count 948, size 25046[0m
2025/06/29 20:28:38.661826 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:38.662842 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_6672_(default|write|lock).sst, key count 958, size 25316[0m
2025/06/29 20:28:38.710967 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:38.712193 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_6763_(default|write|lock).sst, key count 997, size 26369[0m
2025/06/29 20:28:38.713257 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:38.714328 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_6778_(default|write|lock).sst, key count 1003, size 26531[0m
2025/06/29 20:28:38.760850 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:38.762014 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_6869_(default|write|lock).sst, key count 1042, size 27584[0m
2025/06/29 20:28:38.763171 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:38.764205 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_6885_(default|write|lock).sst, key count 1048, size 27746[0m
2025/06/29 20:28:38.812048 peer_storage.go:182: [0;37m[info] [region 1] 3 requesting snapshot[0m
2025/06/29 20:28:38.813251 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore2065506233/snap/gen_1_6_6976_(default|write|lock).sst, key count 1097, size 29069[0m
2025/06/29 20:28:39.168057 test_test.go:104: [0;37m[info] partition: [2 3 4 5], [1][0m
2025/06/29 20:28:39.757273 test_test.go:104: [0;37m[info] partition: [1 3 4 5], [2][0m
2025/06/29 20:28:40.387416 test_test.go:104: [0;37m[info] partition: [2 3 5], [1 4][0m
2025/06/29 20:28:40.896475 test_test.go:104: [0;37m[info] partition: [2], [1 3 4 5][0m
2025/06/29 20:28:41.407542 test_test.go:104: [0;37m[info] partition: [5], [1 2 3 4][0m
2025/06/29 20:28:42.030574 test_test.go:104: [0;37m[info] partition: [2 5], [1 3 4][0m
2025/06/29 20:28:42.547612 test_test.go:104: [0;37m[info] partition: [3 4], [1 2 5][0m
2025/06/29 20:28:43.153650 test_test.go:104: [0;37m[info] partition: [1 3 4], [2 5][0m
2025/06/29 20:28:44.055554 peer_storage.go:182: [0;37m[info] [region 1] 2 requesting snapshot[0m
2025/06/29 20:28:44.061676 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore706647814/snap/gen_1_6_6976_(default|write|lock).sst, key count 1097, size 29069[0m
2025/06/29 20:28:44.161829 peer_storage.go:182: [0;37m[info] [region 1] 2 requesting snapshot[0m
2025/06/29 20:28:44.162047 peer_storage.go:336: [0;37m[info] [region 1] 1 begin to apply snapshot[0m
2025/06/29 20:28:44.163789 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore706647814/snap/gen_1_16_6987_(default|write|lock).sst, key count 1099, size 29123[0m
2025/06/29 20:28:44.164953 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 105 raft logs, takes 2.887084ms[0m
2025/06/29 20:28:44.164977 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:44.165915 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:44.187764 test_test.go:270: [0;33m[warning] shutdown servers
[0m
2025/06/29 20:28:44.187787 node.go:198: [0;37m[info] stop raft store thread, storeID: 1[0m
2025/06/29 20:28:44.200603 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:44.200626 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 34.607667ms][0m
2025/06/29 20:28:44.201253 node.go:198: [0;37m[info] stop raft store thread, storeID: 2[0m
2025/06/29 20:28:44.201305 node.go:198: [0;37m[info] stop raft store thread, storeID: 3[0m
2025/06/29 20:28:44.201350 node.go:198: [0;37m[info] stop raft store thread, storeID: 4[0m
2025/06/29 20:28:44.201376 node.go:198: [0;37m[info] stop raft store thread, storeID: 5[0m
2025/06/29 20:28:44.701521 test_test.go:277: [0;33m[warning] restart servers
[0m
2025/06/29 20:28:44.702894 node.go:193: [0;37m[info] start raft store node, storeID: 1[0m
2025/06/29 20:28:44.704956 peer.go:41: [0;37m[info] region id:1 region_epoch:<conf_ver:1 version:1 > peers:<id:4 store_id:4 > peers:<id:5 store_id:5 > peers:<id:1 store_id:1 > peers:<id:2 store_id:2 > peers:<id:3 store_id:3 >  create peer with ID 1[0m
2025/06/29 20:28:44.705055 raftstore.go:174: [0;37m[info] start store 1, region_count 1, tombstone_count 0, takes 211.291µs[0m
2025/06/29 20:28:44.707636 node.go:193: [0;37m[info] start raft store node, storeID: 2[0m
2025/06/29 20:28:44.709032 peer.go:41: [0;37m[info] region id:1 region_epoch:<conf_ver:1 version:1 > peers:<id:4 store_id:4 > peers:<id:5 store_id:5 > peers:<id:1 store_id:1 > peers:<id:2 store_id:2 > peers:<id:3 store_id:3 >  create peer with ID 2[0m
2025/06/29 20:28:44.709109 raftstore.go:174: [0;37m[info] start store 2, region_count 1, tombstone_count 0, takes 130.875µs[0m
2025/06/29 20:28:44.717814 node.go:193: [0;37m[info] start raft store node, storeID: 3[0m
2025/06/29 20:28:44.719276 peer.go:41: [0;37m[info] region id:1 region_epoch:<conf_ver:1 version:1 > peers:<id:4 store_id:4 > peers:<id:5 store_id:5 > peers:<id:1 store_id:1 > peers:<id:2 store_id:2 > peers:<id:3 store_id:3 >  create peer with ID 3[0m
2025/06/29 20:28:44.719361 raftstore.go:174: [0;37m[info] start store 3, region_count 1, tombstone_count 0, takes 145µs[0m
2025/06/29 20:28:44.720360 node.go:193: [0;37m[info] start raft store node, storeID: 4[0m
2025/06/29 20:28:44.720603 peer.go:41: [0;37m[info] region id:1 region_epoch:<conf_ver:1 version:1 > peers:<id:4 store_id:4 > peers:<id:5 store_id:5 > peers:<id:1 store_id:1 > peers:<id:2 store_id:2 > peers:<id:3 store_id:3 >  create peer with ID 4[0m
2025/06/29 20:28:44.720694 raftstore.go:174: [0;37m[info] start store 4, region_count 1, tombstone_count 0, takes 121.75µs[0m
2025/06/29 20:28:44.721694 node.go:193: [0;37m[info] start raft store node, storeID: 5[0m
2025/06/29 20:28:44.722470 peer.go:41: [0;37m[info] region id:1 region_epoch:<conf_ver:1 version:1 > peers:<id:4 store_id:4 > peers:<id:5 store_id:5 > peers:<id:1 store_id:1 > peers:<id:2 store_id:2 > peers:<id:3 store_id:3 >  create peer with ID 5[0m
2025/06/29 20:28:44.722544 raftstore.go:174: [0;37m[info] start store 5, region_count 1, tombstone_count 0, takes 139.542µs[0m
2025/06/29 20:28:45.673355 peer_storage.go:182: [0;37m[info] [region 1] 5 requesting snapshot[0m
2025/06/29 20:28:45.674893 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore3497283838/snap/gen_1_16_6987_(default|write|lock).sst, key count 1099, size 29123[0m
2025/06/29 20:28:45.681657 peer_storage.go:336: [0;37m[info] [region 1] 4 begin to apply snapshot[0m
2025/06/29 20:28:45.682281 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 105 raft logs, takes 607.584µs[0m
2025/06/29 20:28:45.682296 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:45.683076 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:45.712178 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:45.712201 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 29.043291ms][0m
2025/06/29 20:28:45.876103 peer_storage.go:182: [0;37m[info] [region 1] 5 requesting snapshot[0m
2025/06/29 20:28:45.877521 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore3497283838/snap/gen_1_7_7071_(default|write|lock).sst, key count 1017, size 27001[0m
2025/06/29 20:28:45.884674 peer_storage.go:336: [0;37m[info] [region 1] 4 begin to apply snapshot[0m
2025/06/29 20:28:45.885050 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 104 raft logs, takes 356.958µs[0m
2025/06/29 20:28:45.885076 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:45.886262 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:45.911116 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:45.911140 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 24.7835ms][0m
2025/06/29 20:28:46.076798 peer_storage.go:182: [0;37m[info] [region 1] 5 requesting snapshot[0m
2025/06/29 20:28:46.079006 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore3497283838/snap/gen_1_7_7171_(default|write|lock).sst, key count 918, size 24346[0m
2025/06/29 20:28:46.085517 peer_storage.go:336: [0;37m[info] [region 1] 4 begin to apply snapshot[0m
2025/06/29 20:28:46.085854 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 103 raft logs, takes 322.875µs[0m
2025/06/29 20:28:46.085873 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:46.087011 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:46.114237 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:46.114256 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 27.15975ms][0m
2025/06/29 20:28:46.326450 peer_storage.go:182: [0;37m[info] [region 1] 5 requesting snapshot[0m
2025/06/29 20:28:46.328228 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore3497283838/snap/gen_1_7_7281_(default|write|lock).sst, key count 810, size 21506[0m
2025/06/29 20:28:46.335487 peer_storage.go:336: [0;37m[info] [region 1] 4 begin to apply snapshot[0m
2025/06/29 20:28:46.335951 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 102 raft logs, takes 453.041µs[0m
2025/06/29 20:28:46.335965 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:46.337178 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:46.361145 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:46.361168 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 23.88375ms][0m
2025/06/29 20:28:46.576914 peer_storage.go:182: [0;37m[info] [region 1] 5 requesting snapshot[0m
2025/06/29 20:28:46.578136 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore3497283838/snap/gen_1_7_7391_(default|write|lock).sst, key count 701, size 18597[0m
2025/06/29 20:28:46.585562 peer_storage.go:336: [0;37m[info] [region 1] 4 begin to apply snapshot[0m
2025/06/29 20:28:46.586168 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 101 raft logs, takes 588.458µs[0m
2025/06/29 20:28:46.586181 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:46.586984 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:46.609227 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:46.609254 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 22.188708ms][0m
2025/06/29 20:28:46.777356 peer_storage.go:182: [0;37m[info] [region 1] 5 requesting snapshot[0m
2025/06/29 20:28:46.779005 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore3497283838/snap/gen_1_7_7491_(default|write|lock).sst, key count 603, size 16004[0m
2025/06/29 20:28:46.785547 peer_storage.go:336: [0;37m[info] [region 1] 4 begin to apply snapshot[0m
2025/06/29 20:28:46.786422 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 100 raft logs, takes 859.167µs[0m
2025/06/29 20:28:46.786435 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:46.787141 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:46.805122 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:46.805137 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 17.896167ms][0m
2025/06/29 20:28:46.976932 peer_storage.go:182: [0;37m[info] [region 1] 5 requesting snapshot[0m
2025/06/29 20:28:46.978156 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore3497283838/snap/gen_1_7_7592_(default|write|lock).sst, key count 503, size 13361[0m
2025/06/29 20:28:46.986073 peer_storage.go:336: [0;37m[info] [region 1] 4 begin to apply snapshot[0m
2025/06/29 20:28:46.986897 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 99 raft logs, takes 807.666µs[0m
2025/06/29 20:28:46.986908 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:46.987786 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:47.012091 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:47.012121 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 24.242583ms][0m
2025/06/29 20:28:47.228161 peer_storage.go:182: [0;37m[info] [region 1] 5 requesting snapshot[0m
2025/06/29 20:28:47.229901 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore3497283838/snap/gen_1_7_7697_(default|write|lock).sst, key count 400, size 10621[0m
2025/06/29 20:28:47.239636 peer_storage.go:336: [0;37m[info] [region 1] 4 begin to apply snapshot[0m
2025/06/29 20:28:47.240632 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 98 raft logs, takes 978.958µs[0m
2025/06/29 20:28:47.240651 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:47.241122 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:47.261130 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:47.261147 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 19.917875ms][0m
2025/06/29 20:28:47.426322 peer_storage.go:182: [0;37m[info] [region 1] 5 requesting snapshot[0m
2025/06/29 20:28:47.427808 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore3497283838/snap/gen_1_7_7799_(default|write|lock).sst, key count 299, size 7963[0m
2025/06/29 20:28:47.434524 peer_storage.go:336: [0;37m[info] [region 1] 4 begin to apply snapshot[0m
2025/06/29 20:28:47.435536 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 97 raft logs, takes 999.708µs[0m
2025/06/29 20:28:47.435550 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:47.435947 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:47.460089 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:47.460111 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 24.063708ms][0m
2025/06/29 20:28:47.625674 peer_storage.go:182: [0;37m[info] [region 1] 5 requesting snapshot[0m
2025/06/29 20:28:47.626862 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore3497283838/snap/gen_1_7_7900_(default|write|lock).sst, key count 200, size 5318[0m
2025/06/29 20:28:47.633487 peer_storage.go:336: [0;37m[info] [region 1] 4 begin to apply snapshot[0m
2025/06/29 20:28:47.635055 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 96 raft logs, takes 1.555291ms[0m
2025/06/29 20:28:47.635070 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:47.635548 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:47.659168 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:47.659190 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 23.529291ms][0m
2025/06/29 20:28:47.876554 peer_storage.go:182: [0;37m[info] [region 1] 5 requesting snapshot[0m
2025/06/29 20:28:47.878424 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore3497283838/snap/gen_1_7_8007_(default|write|lock).sst, key count 94, size 2538[0m
2025/06/29 20:28:47.886665 peer_storage.go:336: [0;37m[info] [region 1] 4 begin to apply snapshot[0m
2025/06/29 20:28:47.888297 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 95 raft logs, takes 1.613458ms[0m
2025/06/29 20:28:47.888317 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:47.888663 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:47.916184 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:47.916203 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 27.435583ms][0m
2025/06/29 20:28:49.079388 peer_storage.go:182: [0;37m[info] [region 1] 5 requesting snapshot[0m
2025/06/29 20:28:49.080713 snap.go:549: [0;37m[info] region 1 scan snapshot /var/folders/82/n_z1256j57d_cy9c4jgs1n2r0000gp/T/test-raftstore3497283838/snap/gen_1_7_8140_(default|write|lock).sst, key count 21, size 525[0m
2025/06/29 20:28:49.087902 peer_storage.go:336: [0;37m[info] [region 1] 4 begin to apply snapshot[0m
2025/06/29 20:28:49.089905 peer_storage.go:298: [0;37m[info] [region 1] clear peer 1 meta key 1 apply key 1 raft key and 94 raft logs, takes 1.987416ms[0m
2025/06/29 20:28:49.089921 region_task.go:93: [0;37m[info] begin apply snap data. [regionId: 1][0m
2025/06/29 20:28:49.090179 region_task.go:137: [0;37m[info] succeed in deleting data in range. [regionId: 1, startKey: , endKey: ][0m
2025/06/29 20:28:49.108224 snap.go:700: [0;37m[info] apply snapshot ingested 1 tables[0m
2025/06/29 20:28:49.108243 region_task.go:117: [0;37m[info] applying new data. [regionId: 1, timeTakes: 17.957625ms][0m
2025/06/29 20:28:49.123915 log.go:200: leaderCommit called with invalid index: 8275, err : requested index is unavailable due to compaction
panic: leaderCommit called with invalid index: 8275, err : requested index is unavailable due to compaction

goroutine 424 [running]:
log.(*Logger).Panicf(0x14000124c00, {0x102c8bf90?, 0x1400038d008?}, {0x1417c88d7b8?, 0x1417c88d898?, 0x102b023d8?})
	/usr/local/go/src/log/log.go:310 +0x74
github.com/pingcap-incubator/tinykv/log.(*Logger).Panicf(...)
	/Users/<USER>/WorkSpace/tinykv/log/log.go:200
github.com/pingcap-incubator/tinykv/log.Panicf(...)
	/Users/<USER>/WorkSpace/tinykv/log/log.go:89
github.com/pingcap-incubator/tinykv/raft.leaderCommit(0x14228b37e60, 0x2053)
	/Users/<USER>/WorkSpace/tinykv/raft/raft.go:415 +0xe4
github.com/pingcap-incubator/tinykv/raft.(*Raft).handleAppendResponse(0x14228b37e60, {0x4, 0x5, 0x4, 0x7, 0x0, 0x2053, {0x0, 0x0, 0x0}, ...})
	/Users/<USER>/WorkSpace/tinykv/raft/raft.go:782 +0x208
github.com/pingcap-incubator/tinykv/raft.(*Raft).Step(0x14228b37e60, {0x4, 0x5, 0x4, 0x7, 0x0, 0x2053, {0x0, 0x0, 0x0}, ...})
	/Users/<USER>/WorkSpace/tinykv/raft/raft.go:487 +0x288
github.com/pingcap-incubator/tinykv/raft.(*RawNode).Step(0x14228b150e0, {0x4, 0x5, 0x4, 0x7, 0x0, 0x2053, {0x0, 0x0, 0x0}, ...})
	/Users/<USER>/WorkSpace/tinykv/raft/rawnode.go:147 +0xcc
github.com/pingcap-incubator/tinykv/kv/raftstore.(*peerMsgHandler).onRaftMsg(0x1417c88de18, 0x1420797ef80)
	/Users/<USER>/WorkSpace/tinykv/kv/raftstore/peer_msg_handler.go:415 +0x26c
github.com/pingcap-incubator/tinykv/kv/raftstore.(*peerMsgHandler).HandleMsg(0x1417c88de18, {0x1417c88deb0?, 0x1?, {0x102ee3020?, 0x1420797ef80?}})
	/Users/<USER>/WorkSpace/tinykv/kv/raftstore/peer_msg_handler.go:242 +0x60
github.com/pingcap-incubator/tinykv/kv/raftstore.(*raftWorker).run(0x14183bd9600, 0x14228b47810, 0x0?)
	/Users/<USER>/WorkSpace/tinykv/kv/raftstore/raft_worker.go:54 +0x360
created by github.com/pingcap-incubator/tinykv/kv/raftstore.(*Raftstore).startWorkers in goroutine 52
	/Users/<USER>/WorkSpace/tinykv/kv/raftstore/raftstore.go:270 +0x118
FAIL	github.com/pingcap-incubator/tinykv/kv/test_raftstore	25.644s
FAIL
rm -rf /tmp/*test-raftstore*
